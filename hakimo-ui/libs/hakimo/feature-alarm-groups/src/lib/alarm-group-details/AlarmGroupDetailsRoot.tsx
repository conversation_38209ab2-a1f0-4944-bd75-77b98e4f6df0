import { useAlarmGroupDetails } from '@hakimo-ui/hakimo/data-access';
import { AlarmGroupContainer } from '@hakimo-ui/hakimo/feature-shared';
import { NotFound, LoadingIndicator } from '@hakimo-ui/hakimo/ui-elements';
import { withAuthz, withErrorBoundary } from '@hakimo-ui/hakimo/util';
import { useParams } from 'react-router-dom';

type Params = {
  alarmGroupId: string;
};

interface Props {
  alarmGroupId: string;
}

function AlarmGroupDetailsData(props: Props) {
  const { alarmGroupId } = props;

  const { data, error, isLoading } = useAlarmGroupDetails(alarmGroupId);

  if (isLoading) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-onlight-bg-1 dark:bg-ondark-bg-1">
        <LoadingIndicator text="Loading Alarm group" />
      </div>
    );
  }

  if (error) {
    return <NotFound message="Error loading alarm group details" />;
  }

  if (!data) {
    return <NotFound message="Alarm group not found" />;
  }

  return <AlarmGroupContainer alarmGroup={data} isFullPageMode={true} showSOP={false} />;
}

function AlarmGroupDetailsRoot() {
  const { alarmGroupId } = useParams<Params>();

  if (alarmGroupId === undefined) {
    return <NotFound />;
  }

  return (
    <div className="m-8">
      <AlarmGroupDetailsData alarmGroupId={alarmGroupId} />
    </div>
  );
}

export default withAuthz(withErrorBoundary(AlarmGroupDetailsRoot), [
  'alarm_group:view',
]);
