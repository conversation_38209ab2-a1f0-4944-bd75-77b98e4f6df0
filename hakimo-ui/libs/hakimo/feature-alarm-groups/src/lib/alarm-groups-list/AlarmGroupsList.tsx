import {
  useAlarmGroupDetails,
  useAlarmGroups,
} from '@hakimo-ui/hakimo/data-access';
import {
  ListView,
  AlarmGroupContainer,
} from '@hakimo-ui/hakimo/feature-shared';
import { UnifiedAlarmGroup, TimePeriod } from '@hakimo-ui/hakimo/types';
import { SplitView, SplitViewWrapper, LoadingIndicator } from '@hakimo-ui/hakimo/ui-elements';
import { Page } from '@hakimo-ui/hakimo/ui-layout';
import { withAuthz, withErrorBoundary } from '@hakimo-ui/hakimo/util';
import { Alert } from '@hakimo-ui/shared/ui-base';
import { useAtom, useAtomValue } from 'jotai';
import { useEffect, useState } from 'react';
import FiltersHeader from './FiltersHeader';
import ListItem from './list-item/ListItem';
import { pageAtom, pageSizeAtom, timePeriodAtom } from './state';
import { getSearchParams } from './utils';

export function AlarmGroupsList() {
  const [page, setPage] = useAtom(pageAtom);
  const pageSize = useAtomValue(pageSizeAtom);
  const [period, setPeriod] = useAtom(timePeriodAtom);
  const [total, setTotal] = useState(0);
  const [alarmGroupsData, setAlarmGroupsData] = useState<UnifiedAlarmGroup[]>(
    []
  );
  const [selectedId, setSelectedId] = useState<string>();

  const onData = (data: any) => {
    setTotal(data.total);
    setAlarmGroupsData(data.items as UnifiedAlarmGroup[]);
  };

  const { error, isFetching } = useAlarmGroups(
    getSearchParams(page, pageSize, period),
    undefined,
    onData,
    false,
    true
  );

  const { data: selectedAlarmGroup, isLoading: isLoadingDetails } =
    useAlarmGroupDetails(selectedId, undefined, !selectedId);

  const handleChangePage = (pageNum: number) => {
    setPage(pageNum);
  };

  const handleChangePeriod = (newPeriod: TimePeriod) => {
    setPeriod(newPeriod);
    setPage(1);
  };

  const handleSelectAlarmGroup = (alarmGroup: UnifiedAlarmGroup) => {
    setSelectedId(alarmGroup.id);
  };

  useEffect(() => {
    if (!alarmGroupsData.length) {
      return;
    }
    const shouldSetSelectedItem =
      !selectedId || !alarmGroupsData.some((item) => item.id === selectedId);
    if (shouldSetSelectedItem) {
      setSelectedId(alarmGroupsData[0].id);
    }
  }, [alarmGroupsData, selectedId]);

  return (
    <Page title="Alarm Groups" fullWidth>
      {error ? (
        <Alert type="error">Error getting alarm groups</Alert>
      ) : (
        <SplitViewWrapper>
          <SplitView>
            <div className="flex max-h-full w-[24rem] flex-col divide-y dark:divide-white/10">
              <FiltersHeader
                period={period}
                onChangePeriod={handleChangePeriod}
                isLoading={isFetching}
              />
              <div className="overflow-auto scroll-smooth py-4 pb-4 pl-4 pr-4">
                <ListView
                  items={alarmGroupsData}
                  total={total}
                  page={page}
                  pageSize={pageSize}
                  onChangePage={handleChangePage}
                  renderListItem={(alarmGroup) => (
                    <ListItem
                      alarmGroup={alarmGroup}
                      selected={alarmGroup.id === selectedId}
                      onClick={() => handleSelectAlarmGroup(alarmGroup)}
                    />
                  )}
                />
              </div>
            </div>
          </SplitView>
          <SplitView>
            <div className="ml-4 h-full">
              {isLoadingDetails && (
                <div className="flex h-full items-center justify-center">
                  <LoadingIndicator text="Loading Alarm group" />
                </div>
              )}
              {selectedAlarmGroup ? (
                <AlarmGroupContainer
                  alarmGroup={
                    selectedAlarmGroup as unknown as UnifiedAlarmGroup
                  }
                  isScanAlarmGroup={false}
                  showSOP={false}
                />
              ) : (
                <div className="flex h-full items-center justify-center">
                  No Alarm Group found
                </div>
              )}
            </div>
          </SplitView>
        </SplitViewWrapper>
      )}
    </Page>
  );
}

export default withAuthz(withErrorBoundary(AlarmGroupsList), [
  'alarm_group:view',
]);
