import { AlarmGroupsDTO } from '@hakimo-ui/hakimo/types';
import useAuthenticatedRequest from '../shared/useAuthenticatedRequest';

export function useAlarmGroups(
  searchParams: string,
  refetchInterval?: number,
  onSuccess?: (data: AlarmGroupsDTO) => void,
  disabled = false,
  keepPreviousData = false
) {
  // const host = window.location.hostname;
  // const baseUrl =
  //   host === 'localhost'
  //     ? `http://${host}:${window.location.port}`
  //     : `https://event-flow-${host}`;
  const url = `https://event-flow-staging-frontend.i.hakimo.ai/api/alarm_groups?${searchParams}`;
  const request = new Request(url);

  return useAuthenticatedRequest<AlarmGroupsDTO>(request, {
    queryKey: ['alarm_groups', { searchParams }],
    responseModifier: async (response) => {
      const respJson = await response.json();
      return respJson.payload;
    },
    refetchInterval,
    refetchIntervalInBackground: true,
    onSuccess,
    enabled: !disabled,
    keepPreviousData,
  });
}

export default useAlarmGroups;
