import { AlarmGroup } from '@hakimo-ui/hakimo/types';
import useAuthenticatedRequest from '../shared/useAuthenticatedRequest';

export function useAlarmGroupDetails(
  alarmGroupId: string | undefined,
  onSuccess?: (data: AlarmGroup) => void,
  disabled = false
) {
  // const host = window.location.hostname;
  // const baseUrl =
  //   host === 'localhost'
  //     ? `http://${host}:${window.location.port}`
  //     : `https://event-flow-${host}`;
  const url = `https://event-flow-staging-frontend.i.hakimo.ai/api/alarm_groups/${alarmGroupId}`;
  const request = new Request(url);

  return useAuthenticatedRequest<AlarmGroup>(request, {
    queryKey: ['alarm_group_details', alarmGroupId],
    responseModifier: async (response) => {
      const respJson = await response.json();
      return respJson.payload;
    },
    onSuccess,
    enabled: !disabled && !!alarmGroupId,
  });
}

export default useAlarmGroupDetails;
