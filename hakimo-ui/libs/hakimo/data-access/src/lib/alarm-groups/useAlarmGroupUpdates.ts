import { AlarmGroupUpdatesResponse } from '@hakimo-ui/hakimo/types';
import useAuthenticatedRequest from '../shared/useAuthenticatedRequest';

export function useAlarmGroupUpdates(
  alarmGroupId: string,
  refetchInterval: number
) {
  // const host = window.location.hostname;
  // const baseUrl =
  //   host === 'localhost'
  //     ? `http://${host}:${window.location.port}`
  //     : `https://event-flow-${host}`;
  const url = `https://event-flow-staging-frontend.i.hakimo.ai/api/alarm_groups/${alarmGroupId}/updates`;
  const request = new Request(url);

  return useAuthenticatedRequest<AlarmGroupUpdatesResponse>(request, {
    queryKey: ['alarm_group_updates', alarmGroupId],
    responseModifier: async (response) => {
      const respJson = await response.json();
      return respJson;
    },
    refetchInterval,
  });
}

export default useAlarmGroupUpdates;
