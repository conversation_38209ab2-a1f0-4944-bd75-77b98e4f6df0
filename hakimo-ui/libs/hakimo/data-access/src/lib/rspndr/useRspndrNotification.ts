import { RspndrNotify } from '@hakimo-ui/hakimo/types';
import { useAuthenticatedMutation } from '../shared/useAuthenticatedMutation';

export function useRspndrNotification(
  onSuccess?: (rspndr: RspndrNotify) => void,
  onError?: (err: Error) => void,
  apiToken?: string,
  tenantId?: string,
  rspndrNotifyUrl?: string
) {
  const url = rspndrNotifyUrl || '';

  const request = new Request(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-RSPNDR-TENANT-ID': tenantId || '',
      'X-RSPNDR-API-TOKEN': apiToken || '',
    },
  });

  return useAuthenticatedMutation<RspndrNotify, RspndrNotify | null>(request, {
    onSuccessInvalidationKeys: [['rspndr']],
    onSuccess,
    onError,
  });
}

export default useRspndrNotification;
