import useAuthenticatedMutation from '../shared/useAuthenticatedMutation';

interface RspndrEscalationUpdateRequest {
  rspndr_details?: Record<string, any>;
}

export function useUpdateRspndrEscalationUpdate(
  escalationId: string,
  onSuccess?: () => void,
  onError?: (err: Error) => void
) {
  const url = `/v2/orm/escalations/${escalationId}/rspndr-update`;
  const request = new Request(url, {
    method: 'POST',
  });

  return useAuthenticatedMutation<null, RspndrEscalationUpdateRequest>(
    request,
    {
      onSuccessInvalidationKeys: [['escalation_events', escalationId]],
      onSuccess,
      onError,
    }
  );
}

export default useUpdateRspndrEscalationUpdate;
