/* eslint-disable max-lines */
import {
  useLocationMonitoringSchedule,
  useSOP,
  useUpdateLocationAlarm,
} from '@hakimo-ui/hakimo/data-access';
import {
  LocationAlarmCamera,
  LocationAlarmStatus,
  SOPWorkflow,
  StatusType,
} from '@hakimo-ui/hakimo/types';
import { SOPWorkflowStateContext, toast } from '@hakimo-ui/hakimo/util';
import { Al<PERSON>, Button } from '@hakimo-ui/shared/ui-base';
import clsx from 'clsx';
import { useAtom } from 'jotai';
import { useEffect, useState } from 'react';

import { LoadingIndicator } from '@hakimo-ui/hakimo/ui-elements';
import AIRecommendation from './ai-recommendation/AIRecommendation';
import { EscalateAlarm } from './escalate/EscalateAlarm';
import { ResolveAction } from './ResolveAction';
import { SOPWorkflowRoot } from './SOPWorkflowRoot';
import {
  currentStepAtom,
  DayLocationMonitoringConfig,
  getTodaysMonitoringConfig,
  updateDefaultValues,
  WorkflowItemType,
} from './util';
import WorkflowHeader from './WorkflowHeader';

interface Props {
  locationId: string;
  locationAlarmId: string;
  alarmCameras: LocationAlarmCamera[];
  onAlarmResolve: (status: StatusType, comment?: string) => void;
  locationTenantId: string;
  locationAlarmStatus?: LocationAlarmStatus;
  escalatedAlarmId?: string;
  isScanContext?: boolean;
  onEscalate?: (comment: string) => void;
  onQuickResolve?: (comment: string) => void;
  onCallFromSOP?: (twilioCallSid: string, toName: string) => void;
  escalationId?: string;
}

export function SOPWorkflowContainer(props: Props) {
  const {
    locationId,
    locationAlarmId,
    alarmCameras,
    onAlarmResolve,
    escalatedAlarmId,
    locationAlarmStatus,
    locationTenantId,
    isScanContext = false,
    onEscalate,
    onQuickResolve,
    onCallFromSOP,
    escalationId,
  } = props;
  const [sopWorkflow, setSOPWorkflow] = useState<SOPWorkflow>();
  const [isQuickResolve, setIsQuickResolve] = useState<boolean | undefined>(
    false
  );
  const [selectedSituationText, setSelectedSituationText] = useState('');
  const [locationMonitoringConfigToday, setLocationMonitoringConfigToday] =
    useState<DayLocationMonitoringConfig>();
  const [currentAtomStep, setCurrentAtomStep] = useAtom(currentStepAtom);

  const { data: monitoringScheduleData } = useLocationMonitoringSchedule(
    locationId ? +locationId : 0
  );
  const monitoringConfigData = locationId ? monitoringScheduleData : undefined;

  useEffect(() => {
    if (monitoringConfigData) {
      const motionDetectionWindow = monitoringConfigData.motionDetectionWindow;

      setLocationMonitoringConfigToday(
        getTodaysMonitoringConfig(motionDetectionWindow)
      );
    }
  }, [monitoringConfigData]);

  const resolveMutation = useUpdateLocationAlarm(
    isScanContext ? '' : locationAlarmId,
    () => {
      toast('Alarm status updated', { type: 'success' });
      onAlarmResolve(StatusType.RESOLVED);
    }
  );

  const onSubmitResolveAction = async (comment: string) => {
    if (isScanContext) {
      onQuickResolve?.(comment);
    } else {
      resolveMutation.mutate({ status: StatusType.RESOLVED, comment });
    }
  };

  const onQuickResolveSubmit = (comment: string) => {
    if (isScanContext) {
      onQuickResolve?.(comment);
    } else {
      onSubmitResolveAction(comment);
    }
  };
  const {
    isLoading,
    isRefetching,
    isError,
    error,
    data: sopData,
  } = useSOP(
    {
      locationId,
    },
    async ({ sop_workflow: workflow }) => {
      if (workflow) {
        const updatedWorkflow = updateDefaultValues(workflow);
        setSOPWorkflow(updatedWorkflow);
      }
    }
  );
  const isCurrentAlarmEscalated =
    escalatedAlarmId && escalatedAlarmId === locationAlarmId;
  const [currentWorkflowItemType, setCurrentWorkflowItemType] = useState<
    WorkflowItemType | undefined
  >(
    isCurrentAlarmEscalated ? WorkflowItemType.ESCALATE : WorkflowItemType.STEPS
  );

  useEffect(() => {
    setCurrentAtomStep(0);
  }, [locationAlarmId, setCurrentAtomStep]);

  useEffect(() => {
    if (locationAlarmStatus === 'Resolved') {
      setCurrentWorkflowItemType(undefined);
    }
  }, [locationAlarmStatus]);

  useEffect(() => {
    if (
      currentAtomStep === 0 &&
      currentWorkflowItemType === WorkflowItemType.STEPS
    ) {
      setSelectedSituationText('');
    }
  }, [currentAtomStep, currentWorkflowItemType]);

  useEffect(() => {
    if (sopData) {
      if (sopData.sop_workflow) {
        const updatedWorkflow = updateDefaultValues(sopData.sop_workflow);
        setSOPWorkflow(updatedWorkflow);
      }
    }
  }, [sopData]);

  const navigateTo = (val: WorkflowItemType) => () => {
    setCurrentWorkflowItemType(val);
    if (val === WorkflowItemType.STEPS && currentAtomStep === 0) {
      setSelectedSituationText('');
    }
  };

  const navigateToResolve = (isQuickResolveVal?: boolean) => {
    navigateTo(WorkflowItemType.RESOLVE)();
    setIsQuickResolve(isQuickResolveVal === true); // explicitly checking with true because in other cases click event data is coming
  };

  const onStepChange = (step: number) => {
    setCurrentAtomStep(step);
    if (step === 0) {
      setSelectedSituationText('');
    }
  };

  const onUpdateSituationsText = (actiontext: string) =>
    setSelectedSituationText(actiontext);

  const logCallFromSOP = (twilioCallSid: string, toName: string) => {
    onCallFromSOP?.(twilioCallSid, toName);
  };

  return (
    <SOPWorkflowStateContext.Provider value={{ logCallFromSOP }}>
      <div className="relative min-h-[40rem] min-w-32">
        {(isError || sopWorkflow === undefined) && (
          <div className="mx-auto max-w-screen-2xl p-6">
            {error?.message && (
              <Alert type="error">{error.message ?? ''}</Alert>
            )}
            {!(isLoading || isRefetching) && (
              <span>Please ask your admin to update the SOP.</span>
            )}
          </div>
        )}
        {(isLoading || isRefetching) && (
          <div className="absolute inset-0 z-10 flex h-full items-center justify-center p-2 opacity-70">
            <LoadingIndicator />
          </div>
        )}

        {sopWorkflow && (
          <div
            className={clsx(
              currentWorkflowItemType === WorkflowItemType.STEPS &&
                'flex min-h-[25rem] flex-col items-center justify-between gap-3'
            )}
          >
            {locationAlarmStatus === 'Resolved' && (
              <div>This location alarm is already resolved</div>
            )}

            <div className="divide-onlight-line-3 dark:divide-ondark-line-3 relative w-full space-y-4 divide-y">
              {currentWorkflowItemType !== WorkflowItemType.RESOLVE &&
                locationAlarmStatus !== 'Resolved' &&
                sopWorkflow && (
                  <div>
                    <div className="flex items-start">
                      <WorkflowHeader
                        totalSteps={
                          currentWorkflowItemType === WorkflowItemType.STEPS
                            ? sopWorkflow.talkdowns
                              ? sopWorkflow.talkdowns.length + 1
                              : 1
                            : 1
                        }
                        currentStep={
                          currentWorkflowItemType === WorkflowItemType.STEPS
                            ? currentAtomStep
                            : 0
                        }
                        onStepChange={
                          currentWorkflowItemType === WorkflowItemType.STEPS
                            ? onStepChange
                            : undefined
                        }
                        locationMonitoringConfigToday={
                          locationMonitoringConfigToday
                        }
                        isZeroToleranceSite={sopWorkflow.isZeroTolerance}
                        exceptions={sopWorkflow.exceptions}
                        notes={
                          sopWorkflow.notes?.map((note) => note.text) || []
                        }
                        showExceptionsAndNotes={
                          currentWorkflowItemType === WorkflowItemType.ESCALATE
                        }
                      />
                    </div>
                    <AIRecommendation
                      alarmId={locationAlarmId}
                      useCamGroup={isScanContext}
                    />
                    {currentWorkflowItemType === WorkflowItemType.STEPS && (
                      <Button
                        variant="error"
                        onClick={navigateTo(WorkflowItemType.ESCALATE)}
                        className="absolute -top-1 right-8"
                      >
                        Escalate
                      </Button>
                    )}
                  </div>
                )}

              {currentWorkflowItemType === WorkflowItemType.STEPS &&
                alarmCameras.length > 0 && (
                  <SOPWorkflowRoot
                    navigateToEscalate={navigateTo(WorkflowItemType.ESCALATE)}
                    navigateToResolve={navigateToResolve}
                    sopWorkflow={sopWorkflow}
                    alarmCameras={alarmCameras}
                    locationAlarmId={locationAlarmId}
                    onQuickResolve={onQuickResolveSubmit}
                    isResolveSubmitting={resolveMutation.isLoading}
                    selectedSituationsText={selectedSituationText}
                    onUpdateSituationsText={onUpdateSituationsText}
                  />
                )}
              {currentWorkflowItemType === WorkflowItemType.RESOLVE && (
                <ResolveAction
                  onClose={navigateTo(WorkflowItemType.STEPS)}
                  isQuickResolve={isQuickResolve}
                  onSubmitResolveAction={onSubmitResolveAction}
                  isLoading={resolveMutation.isLoading}
                />
              )}
              {currentWorkflowItemType === WorkflowItemType.ESCALATE && (
                <EscalateAlarm
                  escalationPoints={sopWorkflow.escalationPoints}
                  escalationProtocol={sopWorkflow.escalationProtocol || []}
                  isEscalated={!!isCurrentAlarmEscalated}
                  locationAlarmId={locationAlarmId}
                  locationId={locationId}
                  siteAddress={sopWorkflow.siteAddress}
                  siteGoogleMapLocation={sopWorkflow.siteGoogleMapLocation}
                  locationTenantId={locationTenantId}
                  onCancel={navigateTo(WorkflowItemType.STEPS)}
                  selectedSituationText={selectedSituationText}
                  onPreEscalationSubmit={onEscalate}
                  isScanContext={isScanContext}
                  escalationId={escalationId}
                />
              )}
            </div>
          </div>
        )}
      </div>
    </SOPWorkflowStateContext.Provider>
  );
}
