/* eslint-disable max-lines */
import {
  useUpdateRspndrEscalationUpdate,
  useLocationById,
  useRspndrCancelIncidents,
  useRspndrIncidents,
  useTenantDetails,
  useUpdateLocationAlarm,
  useRspndrNotification,
} from '@hakimo-ui/hakimo/data-access';
import {
  IncidentRspndrType,
  Rspndr,
  StatusType,
  Tenant,
} from '@hakimo-ui/hakimo/types';
import { SelectMenu } from '@hakimo-ui/hakimo/ui-elements';
import { toast } from '@hakimo-ui/hakimo/util';
import { Alert, Button, Modal, Textarea } from '@hakimo-ui/shared/ui-base';
import {
  PaperAirplaneIcon,
  XCircleIcon,
  ChatBubbleLeftIcon,
} from '@heroicons/react/24/solid';
import { useState } from 'react';
import { generateHMAC, getCallList } from './util';

interface Props {
  locationId?: number;
  locationAlarmId?: string;
  locationTenantId?: string;
  timestamp?: string;
  toName: string;
  toNumber: string;
  escalationId?: string;
  isScanContext?: boolean;
}
export function ContactRspndr(props: Props) {
  const {
    locationAlarmId,
    locationId,
    locationTenantId,
    toName,
    toNumber,
    escalationId,
    isScanContext = false,
  } = props;
  const [rspndrDetails, setRspndrDetails] = useState<Rspndr | null>(null);
  const [apiToken, setApiToken] = useState<string | null>(null);
  const [tenantId, setTenantId] = useState<string | null>(null);
  const [rspndrUrl, setRspndrUrl] = useState<string | null>(null);
  const [showRspndrModal, setShowRspndrModal] = useState<boolean>(false);
  const [notes, setNotes] = useState<string>('');
  const [rspndrCancelUrl, setRspndrCancelUrl] = useState<string | null>(null);
  const [rspndrNotifyUrl, setRspndrNotifyUrl] = useState<string | null>(null);
  const [showRspndrCancelModal, setShowRspndrCancelModal] =
    useState<boolean>(false);
  const [showRspndrNotifyModal, setShowRspndrNotifyModal] =
    useState<boolean>(false);
  const [cancelNotes, setCancelNotes] = useState<string>('');
  const [notifyNotes, setNotifyNotes] = useState<string>('');
  const incidentTypes: IncidentRspndrType[] = [IncidentRspndrType.PATROL_CHECK];
  const [selectedIncidentType, setSelectedIncidentType] =
    useState<IncidentRspndrType>(incidentTypes[0]);

  const onClick = () => setShowRspndrModal(true);
  const onCloseCb = () => setShowRspndrModal(false);

  const onChangeCancel = (val: string) => {
    setCancelNotes(val);
  };

  const onChangeNotify = (val: string) => {
    setNotifyNotes(val);
  };

  const onChange = (val: string) => {
    setNotes(val);
  };

  const resolveAlarmMutation = useUpdateLocationAlarm(locationAlarmId ?? '');
  const locationDetails = useLocationById((locationId ?? '').toString()).data;

  const onSuccess = (tenant: Tenant) => {
    generateHMAC(
      tenant.config?.rspndrApiKey || '',
      tenant.config?.rspndrApiSecret || ''
    ).then((hmac) => {
      setApiToken(hmac);
    });
    setTenantId(tenant.config?.rspndrTenantId || '');
    setRspndrUrl(tenant.config?.rspndrUrl || '');
    setRspndrCancelUrl(tenant.config?.rspndrCancelUrl || '');
    setRspndrNotifyUrl(tenant.config?.rspndrNotifyUrl || '');
    setRspndrDetails({
      escalationType: tenant.config?.escalationType || '',
      tenantId: tenant.config?.rspndrTenantId || '',
      locationId: locationId ?? 0,
      address1: locationDetails?.description || '',
      name: locationDetails?.name || '',
      city: locationDetails?.city || '',
      state: locationDetails?.state || '',
      country: locationDetails?.country || '',
      postal_code: tenant.config?.postalCode || '',
      incident_id: isScanContext
        ? escalationId
          ? escalationId
          : ''
        : locationAlarmId,
      notes: notes,
      type: selectedIncidentType,
      call_list: getCallList(),
    });
  };

  useTenantDetails(locationTenantId || '', onSuccess);

  const handleRspndrIncidentSuccess = () => {
    toast(`Rspndr incidents created successfully!`, {
      type: 'success',
    });
    onCloseCb();
  };
  const handleRspndrIncidentError = () => {
    toast(`Rspndr incidents creation failed!`, {
      type: 'error',
    });
  };

  const handleRspndrCancelSuccess = () => {
    toast(`Successfully cancelled Rspndr Incident!`, {
      type: 'success',
    });
    onCloseCancel();
  };
  const handleRspndrCancelError = () => {
    toast(`Rspndr incident Cancelation failed!`, {
      type: 'error',
    });
  };
  const handleRspndrNotifySuccess = () => {
    toast(`Successfully sent Rspndr Notification!`, {
      type: 'success',
    });
    onCloseNotify();
  };
  const handleRspndrNotifyError = () => {
    toast(`Rspndr Notification failed!`, {
      type: 'error',
    });
  };

  const mutation = useRspndrIncidents(
    handleRspndrIncidentSuccess,
    handleRspndrIncidentError,
    apiToken ? apiToken : '',
    tenantId ? tenantId : '',
    rspndrUrl ? rspndrUrl : ''
  );

  const mutationCancel = useRspndrCancelIncidents(
    handleRspndrCancelSuccess,
    handleRspndrCancelError,
    apiToken ? apiToken : '',
    tenantId ? tenantId : '',
    rspndrCancelUrl ? rspndrCancelUrl : ''
  );

  const mutationNotify = useRspndrNotification(
    handleRspndrNotifySuccess,
    handleRspndrNotifyError,
    apiToken ? apiToken : '',
    tenantId ? tenantId : '',
    rspndrNotifyUrl ? rspndrNotifyUrl : ''
  );

  const resetCancelMutation = () => {
    if (mutationCancel.reset) {
      mutationCancel.reset();
    }
  };
  const resetNotifyMutation = () => {
    if (mutationNotify.reset) {
      mutationNotify.reset();
    }
  };

  const onClickCancel = () => {
    resetCancelMutation();
    setShowRspndrCancelModal(true);
  };
  const onCloseCancel = () => {
    resetCancelMutation();
    setShowRspndrCancelModal(false);
  };

  const onClickNotify = () => {
    resetNotifyMutation();
    setShowRspndrNotifyModal(true);
  };
  const onCloseNotify = () => {
    resetNotifyMutation();
    setShowRspndrNotifyModal(false);
  };

  const escalationUpdateMutation = useUpdateRspndrEscalationUpdate(
    escalationId || ''
  );

  const onRspndrIncidents = () => {
    if (rspndrDetails) {
      const updatedRspndrDetails: Rspndr = {
        ...rspndrDetails,
        notes: notes,
        call_list: getCallList(),
      };
      setRspndrDetails(updatedRspndrDetails as Rspndr);
      mutation.mutate(updatedRspndrDetails, {
        onSuccess: () => {
          if (isScanContext && escalationId) {
            escalationUpdateMutation.mutate({
              rspndr_details: {
                incident_type: selectedIncidentType,
                notes: notes,
                location_id: locationId,
                incident_id: escalationId,
              },
            });
          } else {
            const resolveAlarmPayload = {
              status: StatusType.IN_PROGRESS,
              comment: `Escalation - RSPNDR incidents created successfully!`,
            };
            resolveAlarmMutation.mutate(resolveAlarmPayload);
          }
        },
        onError: () => {
          toast(`Rspndr incidents creation failed!`, {
            type: 'error',
          });
        },
      });
      setNotes('');
    } else {
      toast(`Rspndr details are not available!`, {
        type: 'error',
      });
      onCloseCb();
    }
  };

  const onRspndrCancelIncident = () => {
    const cancelBody = {
      incident_id: escalationId,
      notes: cancelNotes,
    };
    mutationCancel.mutate(cancelBody);
    setCancelNotes('');
  };

  const onRspndrNotifyIncident = () => {
    if (escalationId) {
      const notifyBody = {
        incident_id: escalationId,
        message: notifyNotes,
      };
      mutationNotify.mutate(notifyBody, {
        onSuccess: () => {
          escalationUpdateMutation.mutate({
            rspndr_details: {
              incident_type: selectedIncidentType,
              notes: notifyNotes,
              location_id: locationId,
              incident_id: escalationId,
            },
          });
        },
        onError: () => {
          toast(`Rspndr notification failed!`, {
            type: 'error',
          });
        },
      });
      setNotifyNotes('');
    } else {
      toast(`Escalation ID is not available!`, {
        type: 'error',
      });
      onCloseNotify();
    }
  };

  const renderActions = ({
    onSend,
    onClose,
    isLoading,
  }: {
    onSend: () => void;
    onClose: () => void;
    isLoading: boolean;
  }) => (
    <>
      <Button variant="primary" onClick={onSend} loading={isLoading}>
        Send
      </Button>
      <Button variant="outline" onClick={onClose} disabled={isLoading}>
        Close
      </Button>
    </>
  );

  return (
    <div>
      <div className="flex gap-2">
        <Button
          variant="icon"
          onClick={onClick}
          title="Rspndr Escalate"
          disabled={!escalationId}
        >
          <PaperAirplaneIcon className="text-primary-500 h-4 w-4" />
        </Button>
        <Button
          variant="icon"
          onClick={onClickCancel}
          title="Rspndr Cancel"
          disabled={!escalationId}
        >
          <XCircleIcon className="text-primary-500 h-4 w-4" />
        </Button>
        <Button
          variant="icon"
          onClick={onClickNotify}
          title="Rspndr Notify"
          disabled={!escalationId}
        >
          <ChatBubbleLeftIcon className="text-primary-500 h-4 w-4" />
        </Button>
      </div>
      {showRspndrModal && (
        <Modal
          open={true}
          onClose={onCloseCb}
          title="RSPNDR Escalate"
          footer={renderActions({
            onSend: onRspndrIncidents,
            onClose: onCloseCb,
            isLoading: mutation.isLoading,
          })}
        >
          <div className="max-w-xs p-4">
            <SelectMenu
              label="Choose an Incident Type"
              items={incidentTypes}
              value={selectedIncidentType}
              onChange={(val) => setSelectedIncidentType(val)}
              displayValue={(val) => val ?? ''}
              placeholder="Select a value"
            />
          </div>
          <div className="w-[30rem] pb-4 pl-4 pr-4">
            {mutation.isError && (
              <div className="px-8 pt-2">
                <Alert type="error">{mutation.error.message}</Alert>
              </div>
            )}
            <div className="mb-4">
              To: {toName} <span className="text-xs">{toNumber}</span>
            </div>
            <Textarea
              rows={3}
              value={notes}
              onChange={onChange}
              placeholder="Write your message here..."
            />
          </div>
        </Modal>
      )}
      {showRspndrCancelModal && (
        <Modal
          open={true}
          onClose={onCloseCancel}
          title="RSPNDR Cancel"
          footer={renderActions({
            onSend: onRspndrCancelIncident,
            onClose: onCloseCancel,
            isLoading: mutationCancel.isLoading,
          })}
        >
          <div className="w-[30rem] p-4">
            {mutationCancel.isError && (
              <div className="px-8 pt-2">
                <Alert type="error">{mutationCancel.error.message}</Alert>
              </div>
            )}
            <div className="mb-4">
              To: {toName} <span className="text-xs">{toNumber}</span>
            </div>
            <Textarea
              rows={3}
              value={cancelNotes}
              onChange={onChangeCancel}
              placeholder="Write your cancel message here..."
            />
          </div>
        </Modal>
      )}
      {showRspndrNotifyModal && (
        <Modal
          open={true}
          onClose={onCloseNotify}
          title="RSPNDR Notify"
          footer={renderActions({
            onSend: onRspndrNotifyIncident,
            onClose: onCloseNotify,
            isLoading: mutationNotify.isLoading,
          })}
        >
          <div className="w-[30rem] p-4">
            {mutationNotify.isError && (
              <div className="px-8 pt-2">
                <Alert type="error">{mutationNotify.error.message}</Alert>
              </div>
            )}
            <div className="mb-4">
              To: {toName} <span className="text-xs">{toNumber}</span>
            </div>
            <Textarea
              rows={3}
              value={notifyNotes}
              onChange={onChangeNotify}
              placeholder="Write your notify message here..."
            />
          </div>
        </Modal>
      )}
    </div>
  );
}
