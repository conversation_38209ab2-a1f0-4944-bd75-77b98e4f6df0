import { render } from '@testing-library/react';
import SOP from './SOP';

jest.mock('@hakimo-ui/hakimo/data-access', () => ({
  useSOP: () => ({
    data: {
      sop_text: 'test',
    },
    loading: false,
    error: null,
  }),
  useUpdateSOP: () => ({
    mutation: {
      mutate: jest.fn(),
    },
  }),
  useCreateSOP: () => ({
    mutation: {
      mutate: jest.fn(),
    },
  }),
  useLocationById: () => ({
    data: {
      id: '123',
      name: 'Test Location',
    },
  }),
}));

describe('SOP', () => {
  it('should render successfully', () => {
    const { baseElement } = render(<SOP locationId="test" />);
    expect(baseElement).toBeTruthy();
  });
});
