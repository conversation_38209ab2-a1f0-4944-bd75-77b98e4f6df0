/* eslint-disable max-lines */
import {
  AlarmCountResponse,
  AlarmGroupResponse,
  AlarmListResponse,
  UnifiedAlarmGroup,
  UnifiedAlarmMessageType,
} from '@hakimo-ui/hakimo/types';
import {
  LoadingIndicator,
  PageLoadingIndicator,
} from '@hakimo-ui/hakimo/ui-elements';
import { Page } from '@hakimo-ui/hakimo/ui-layout';
import { toast, useAlarmDetailWs } from '@hakimo-ui/hakimo/util';
import { Alert } from '@hakimo-ui/shared/ui-base';
import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  ALARM_DETAIL_POLL_INTERVAL,
  PENDING_COUNT_REFRESH_INTERVAL,
} from './constants';
import { MonitoringScreen } from './MonitoringScreen';

import AlarmGroupContainer from './AlarmGroupContainer';
import { useUnifiedAlarmWsMessageHandler } from './hooks/useUnifiedAlarmWsMessageHandler';
import { removeDuplicateAlarms } from './utils';

export function AlarmGroupWrapper() {
  const location = useLocation();
  const pathAlarmId = location.pathname.split('/')[2] ?? undefined;

  const [alarm, setAlarm] = useState<UnifiedAlarmGroup>();
  const [count, setCount] = useState<number | undefined>(undefined);
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const [alarmErrorState, setAlarmErrorState] = useState({
    isError: false,
    errorMessage: '',
  });

  const { send } = useAlarmDetailWs();

  useEffect(() => {
    let timerId: number;
    if (!pathAlarmId) {
      setAlarm(undefined);
      setIsLoading(false);
      timerId = window.setInterval(() => {
        send?.({
          type: 'alarm_count',
          severity: 'high',
          monitoringType: 'locationAlarm',
        });
      }, PENDING_COUNT_REFRESH_INTERVAL);
    } else {
      setIsLoading(true);
      send?.({
        type: 'alarm',
        alarmId: pathAlarmId,
        monitoringType: 'locationAlarm',
      });
    }

    return () => {
      timerId && clearInterval(timerId);
    };
  }, [pathAlarmId, send]);

  const handleAlarmCountResponse = (data: AlarmCountResponse) => {
    setCount(data.count);
  };
  useUnifiedAlarmWsMessageHandler(
    UnifiedAlarmMessageType.ALARM_COUNT,
    handleAlarmCountResponse
  );

  const handleListAlarmsResponse = (data: AlarmListResponse) => {
    if (data.isSuccess === false) {
      toast(`Failed to fetch alarm - ${data.error}`, { type: 'error' });
      setIsLoading(false);
      return;
    }
    if (data.payload && data.payload.length > 0) {
      const alarmGroup = data.payload[0];
      setAlarm(removeDuplicateAlarms(alarmGroup));
      navigate(`/monitoring/${alarmGroup.id}`);
      setIsLoading(false);
    } else {
      toast('Alarm already picked up by operator');
      setIsLoading(false);
    }
  };
  useUnifiedAlarmWsMessageHandler(
    UnifiedAlarmMessageType.LIST_ALARM,
    handleListAlarmsResponse
  );

  useEffect(() => {
    let timer: number;
    if (alarm?.id) {
      timer = window.setInterval(() => {
        send?.({
          type: 'alarm',
          alarmId: alarm.id,
          monitoringType: 'locationAlarm',
          tenantId: alarm.tenantId,
        });
      }, ALARM_DETAIL_POLL_INTERVAL);
    }
    return () => {
      timer && clearInterval(timer);
    };
  }, [alarm?.id, alarm?.tenantId, send]);

  const handleSingleAlarmResponse = (data: AlarmGroupResponse) => {
    if (data.isSuccess === false) {
      toast(`Failed to fetch alarm details - ${data.error}`, {
        type: 'error',
      });
      setAlarmErrorState({
        isError: true,
        errorMessage: `Failed to fetch alarm details. Error: ${data.error}. Please check if the alarm id is correct or contact Hakimo support.`,
      });
      return;
    } else {
      setAlarmErrorState({
        isError: false,
        errorMessage: '',
      });
    }
    if (pathAlarmId && data.payload.id === pathAlarmId) {
      setAlarm(removeDuplicateAlarms(data.payload));
    }
  };

  useUnifiedAlarmWsMessageHandler(
    UnifiedAlarmMessageType.ALARM,
    handleSingleAlarmResponse
  );
  const fetchAlarm = () => {
    setIsLoading(true);
    send?.({
      type: 'list_alarm',
      count: 1,
      severity: 'high',
      monitoringType: 'locationAlarm',
    });
  };

  if (!alarm && pathAlarmId !== undefined) {
    return (
      <Page title="Monitoring">
        {alarmErrorState.isError && (
          <Alert type="error">{alarmErrorState.errorMessage}</Alert>
        )}
        {!alarmErrorState.isError && <PageLoadingIndicator text="Loading Alarm group" />}
      </Page>
    );
  } else if (alarm) {
    return (
      <Page title="Monitoring">
        <AlarmGroupContainer alarmGroup={alarm} />
      </Page>
    );
  } else {
    return (
      <Page title="Monitoring">
        {count !== undefined ? (
          <MonitoringScreen
            count={count}
            isLoading={isLoading}
            fetchAlarm={fetchAlarm}
          />
        ) : (
          <div className="m-auto">
            <LoadingIndicator text="Loading..." />
          </div>
        )}
      </Page>
    );
  }
}

export default AlarmGroupWrapper;
