import {
  LocationAlarmCamera,
  LocationAlarmStatus,
  StatusType,
  AlarmGroup,
  UnifiedAlarmGroup,
} from '@hakimo-ui/hakimo/types';
import { SOPWorkflowContainer } from '../sop-workflow/SOPWorkflowContainer';

export interface Props {
  alarmGroup: AlarmGroup | UnifiedAlarmGroup;
  onAlarmResolve: (status: StatusType) => void;
}

export function SOP(props: Props) {
  const {
    alarmGroup: { id, alarms, location, escalations, tenantId, status },
    onAlarmResolve,
  } = props;

  const alarmCameras: LocationAlarmCamera[] = alarms.map((alarm) => ({
    id: alarm.cameraId,
    name: alarm.cameraName,
    liveStreamUrl: alarm.cameraLiveStreamUrl,
    isTalkdownEnabled: alarm.isTalkDownEnabled,
  }));

  return (
    <SOPWorkflowContainer
      locationAlarmId={id}
      locationId={String(location.id)}
      alarmCameras={alarmCameras}
      onAlarmResolve={onAlarmResolve}
      locationAlarmStatus={status as LocationAlarmStatus}
      escalatedAlarmId={escalations?.[0]?.initiatingLocationAlarmId}
      locationTenantId={tenantId}
    />
  );
}

export default SOP;
