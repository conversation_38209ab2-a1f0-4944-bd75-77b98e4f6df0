import { useState } from 'react';
import { useAlarmGroupProvider } from '../alarm-provider/AlarmGroupProvider';
import AlarmGroupTimeline from './AlarmGroupTimeline';
import TimelineActions from './TimelineActions';

interface Props {
  showNeighbourAlarmActions?: boolean;
}

export function AlarmGroupToolbar(props: Props) {
  const { showNeighbourAlarmActions = true } = props;

  const [isCompressedTimeline, setIsCompressedTimeline] = useState(true);

  const { showMinorAlarms, toggleShowMinorAlarms } = useAlarmGroupProvider();

  const toggleTimeline = () => setIsCompressedTimeline(!isCompressedTimeline);

  return (
    <div className="flex flex-col">
      <TimelineActions
        toggleTimeline={toggleTimeline}
        isCompressedTimeline={isCompressedTimeline}
        toggleShowMinorAlarms={toggleShowMinorAlarms}
        showMinorAlarms={showMinorAlarms}
      />
      <div className="relative mb-10 flex w-full items-center gap-2">
        <div className="flex-grow">
          <AlarmGroupTimeline
            showNeighbourAlarmActions={showNeighbourAlarmActions}
            isCompressedTimeline={isCompressedTimeline}
          />
        </div>
      </div>
    </div>
  );
}

export default AlarmGroupToolbar;
