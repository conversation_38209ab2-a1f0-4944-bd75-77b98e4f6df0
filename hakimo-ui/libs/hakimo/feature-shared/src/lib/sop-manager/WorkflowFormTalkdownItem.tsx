import { SOPTalkdown, SOPTalkdownType } from '@hakimo-ui/hakimo/types';
import {
  Button,
  ItemsSwitch,
  SwitchItem,
  Textarea,
  Tooltip,
} from '@hakimo-ui/shared/ui-base';
import { InformationCircleIcon, TrashIcon } from '@heroicons/react/24/outline';
import { TALKDOWN_TYPE_INFO } from './constants';

interface Props {
  talkdown: SOPTalkdown;
  onChange: (val: SOPTalkdown) => void;
  onDelete: () => void;
  isDeleteEnabled: boolean;
}

export function WorkflowFormTalkdownItem(props: Props) {
  const { talkdown, onChange, onDelete, isDeleteEnabled } = props;

  const switchOptions = [
    {
      id: SOPTalkdownType.STATIC,
      name: 'Static',
    },
    {
      id: SOPTalkdownType.DYANMIC,
      name: 'Dynamic',
    },
  ];

  const onChangeItemsSwitch = (item: SwitchItem) => {
    const updatedTalkdown = { ...talkdown, type: item.id as SOPTalkdownType };
    onChange(updatedTalkdown);
  };

  const onChangeText = (val: string) => {
    const updatedTalkdown = { ...talkdown, text: val };
    onChange(updatedTalkdown);
  };

  return (
    <div className="dark:border-ondark-line-2 relative col-span-2 grid grid-cols-2 gap-2 rounded-md border p-3">
      {isDeleteEnabled && (
        <Button
          variant="icon"
          onClick={onDelete}
          className="absolute right-1 top-0"
        >
          <TrashIcon className="h-5 w-5" />
        </Button>
      )}
      <span className="flex items-center gap-2">
        <span>Talkdown type:</span>
        <Tooltip
          text={TALKDOWN_TYPE_INFO}
          colorModifier="info"
          position="top-right"
          size="large"
        >
          <InformationCircleIcon className="h-5 w-5" />
        </Tooltip>
      </span>
      <ItemsSwitch
        items={switchOptions}
        onChange={onChangeItemsSwitch}
        selectedItem={switchOptions.find((opt) => opt.id === talkdown.type)}
      />
      <span>Talkdown text:</span>
      <Textarea
        placeholder="Provide talkdown text"
        onChange={onChangeText}
        value={talkdown.text}
        rows={3}
      />
    </div>
  );
}

export default WorkflowFormTalkdownItem;
