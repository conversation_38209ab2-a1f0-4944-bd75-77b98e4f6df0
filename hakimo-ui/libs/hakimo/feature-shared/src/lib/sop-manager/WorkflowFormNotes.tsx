import { But<PERSON>, InputField, Toggle } from '@hakimo-ui/shared/ui-base';
import { TrashIcon } from '@heroicons/react/24/outline';
import { SOPNote } from '@hakimo-ui/hakimo/types';

interface Props {
  notes: SOPNote[];
  onChange: (val: SOPNote[]) => void;
}

export function WorkflowFormNotes(props: Props) {
  const { notes, onChange } = props;

  const onInputChange =
    (index: number) => (e: React.ChangeEvent<HTMLInputElement>) => {
      const val = e.target.value;
      const updatedNotes = [...notes];
      updatedNotes[index] = { ...updatedNotes[index], text: val };
      onChange(updatedNotes);
    };

  const onToggleChange = (index: number) => (enabled: boolean) => {
    const updatedNotes = [...notes];
    updatedNotes[index] = {
      ...updatedNotes[index],
      isRelevantForAiOperator: enabled,
    };
    onChange(updatedNotes);
  };

  const onDeleteNote = (index: number) => () => {
    const updatedNotes = [...notes];
    updatedNotes.splice(index, 1);
    onChange(updatedNotes);
  };

  const onAddNote = () => {
    const updatedNotes = [...notes];
    updatedNotes.push({ text: '', isRelevantForAiOperator: true });
    onChange(updatedNotes);
  };

  return (
    <div className="col-span-2 space-y-4">
      {notes.map((note, i) => (
        <div key={i} className="flex items-center gap-4">
          <div className="flex-1">
            <InputField
              value={note.text}
              type="text"
              onChange={onInputChange(i)}
              placeholder="Enter note text..."
            />
          </div>
          <div className="flex-shrink-0">
            <Toggle
              enabled={note.isRelevantForAiOperator}
              onChange={onToggleChange(i)}
              label="AI Operator"
              type="positive"
            />
          </div>
          <div>
            <Button variant="icon" onClick={onDeleteNote(i)}>
              <TrashIcon className="h-5 w-5" />
            </Button>
          </div>
        </div>
      ))}
      <div>
        <Button onClick={onAddNote}>Add item</Button>
      </div>
    </div>
  );
}

export default WorkflowFormNotes;
