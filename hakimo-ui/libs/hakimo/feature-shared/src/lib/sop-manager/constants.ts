/* eslint-disable max-lines */
import {
  Situation,
  SituationColor,
  SOPWorkflowFormItemsType,
  SiteCategoryOption,
} from '@hakimo-ui/hakimo/types';

export const DEFAULT_SOP_SITUATIONS: Situation[] = [
  {
    label: 'Unauthorized Access',
    color: SituationColor.RED,
  },
  {
    label: 'Others',
    color: SituationColor.BLUE,
  },
];

export const DEFAULT_SOP_QUICK_RESOLVE: string[] = [
  'Authorized Person',
  'Authorized Vehicle',
  'False Positive',
  'Others',
];

export const SITE_CATEGORY_OPTIONS: SiteCategoryOption[] = [
  {
    key: 'Auto Dealerships',
    description:
      'Expect to see customers interacting with employees during business hours. They will be inside the business as well as on the parking lots. They will interact with vehicles parked in the lots during business hours.',
  },
  {
    key: 'Construction Site',
    description:
      'Expect to see construction equipment and containers. During monitoring hours, no one except for construction workers are allowed on-site.',
  },
  {
    key: 'Apartment Monitoring',
    description:
      'The footage is from an apartment complex where normal activities such as residents walking, entering/exiting buildings, and vehicles moving are expected.',
  },
  {
    key: 'Vacant Lot',
    description:
      "Expect to see little to no regular activity, perhaps occasional trespassing, dumping, or wildlife. A security camera might capture unauthorized entry, littering, animal movement, and changes in the lot's condition over time.",
  },
  {
    key: 'Storage Facility',
    description:
      'Expect to see customers accessing their storage units, moving belongings, and potentially loading/unloading vehicles. You will see individuals entering and exiting units, vehicles moving within the facility grounds, and people interacting with their storage spaces.',
  },
  {
    key: 'Parking Lot',
    description:
      'Expect to see parked vehicles, people entering and exiting cars, and potentially individuals passing through.',
  },
  {
    key: 'Farm',
    description:
      'Expect to see agricultural activities like planting, harvesting, animal care, and equipment operation around buildings and fields.',
  },
  {
    key: 'Warehouse',
    description:
      'Expect to see loading and unloading of trucks, movement of goods via forklifts or other machinery, and workers managing inventory. Inside, a camera might see the flow of goods, worker activity, and storage organization, while outside it could capture truck arrivals and departures, loading dock operations, and vehicle parking.',
  },
];

export const SOPWorkflowFormItems: SOPWorkflowFormItemsType[] = [
  {
    title: 'Site address',
    id: 'siteAddress',
    inputType: 'string',
  },
  {
    title: 'Site Google map location',
    id: 'siteGoogleMapLocation',
    inputType: 'link',
  },
  {
    title: 'Site Category',
    id: 'siteCategory',
    inputType: 'dropdown',
    dropdownOptions: SITE_CATEGORY_OPTIONS,
  },
  {
    title: 'Site Description',
    id: 'siteCategory',
    inputType: 'siteDescription',
  },
  {
    title: 'Business hours',
    id: 'businessHours',
    inputType: 'businessHours',
    showTimezone: true,
  },
  {
    title: 'Zero Tolerance',
    id: 'isZeroTolerance',
    inputType: 'boolean',
    info: 'When enabled, operator would be escalating the incident directly. Skipping the talkdowns',
  },
  {
    title: 'Site Exceptions',
    id: 'exceptions',
    inputType: 'list',
    isFullColSpan: true,
  },
  {
    title: 'Situations',
    id: 'situations',
    inputType: 'situation',
    info: 'Emergency & Non-Emergency situations which operators should look out',
    isFullColSpan: true,
  },
  {
    title: 'Notes',
    id: 'notes',
    inputType: 'notes',
    isFullColSpan: true,
  },
  {
    title: 'Quick Resolve Actions',
    id: 'quickResolveActions',
    inputType: 'list',
    info: 'These actions will come in 2nd step of SOP workflow. Below mentioned text will be considered as Button text and will resolve the alarm on one click',
    isFullColSpan: true,
  },
  {
    title: 'Talkdown Enabled',
    id: 'isTalkdownEnabled',
    inputType: 'boolean',
  },
  {
    title: 'Talkdowns',
    id: 'talkdowns',
    inputType: 'talkdowns',
    isFullColSpan: true,
  },
  {
    title: 'Talkdown Notes',
    id: 'talkdownNotes',
    inputType: 'list',
    isFullColSpan: true,
  },
  {
    title: 'Escalation Protocol',
    id: 'escalationProtocol',
    inputType: 'list',
    isFullColSpan: true,
  },
  {
    title: 'Points to remember while escalation',
    id: 'escalationPoints',
    inputType: 'list',
    isFullColSpan: true,
  },
];

export const TALKDOWN_TYPE_INFO = `Static Talkdown: Delivers full sentences with immediate text-to-speech playback, requiring no user input.\n
Dynamic Talkdown: Requires user input, such as shirt color, to customize responses. Supports both text-to-speech and recorded delivery.`;
