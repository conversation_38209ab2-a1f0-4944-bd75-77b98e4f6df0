import { SOPWorkflow, SOPNote } from '@hakimo-ui/hakimo/types';

const VALIDATION_ERRORS: { [key: string]: string } = {
  REQUIRED_SITE_ADDRESS: 'Site address is required',
  REQUIRED_GOOGLE_MAP: 'Site Google map location is required',
  REQUIRED_TALKDOWN_TEXT: 'First talkdown text is required',
  REQUIRED_TALKDOWN_TEXT_SECOND: 'Second talkdown text is required',
  REQUIRED_ESCALATION_POINTS:
    'Escalation points are required. Add at least one.',
  EMPTY_ESCALATION_POINT:
    'Escalation point should not be empty. Provide info or delete the empty ones.',
  REQUIRED_EXCEPTION_POINTS:
    'Exceptions points are required. Add at least one.',
  EMPTY_EXCEPTION_POINT:
    'Exceptions point should not be empty. Provide info or delete the empty ones.',
  EMPTY_NOTE:
    'Note should not be empty. Provide info or delete the empty ones.',
  EMPTY_TALKDOWN_NOTE:
    'Talkdown note should not be empty. Provide info or delete the empty ones.',
};

const validatePoints = (type: string, points: string[]) => {
  if (points.length === 0 && type === 'Escalation') {
    return {
      isValid: false,
      message: VALIDATION_ERRORS[`REQUIRED_${type.toUpperCase()}_POINTS`],
    };
  }
  if (points.some((point) => point === '')) {
    return {
      isValid: false,
      message: VALIDATION_ERRORS[`EMPTY_${type.toUpperCase()}_POINT`],
    };
  }
  return { isValid: true, message: '' };
};

const validateNotes = (notes: SOPNote[]) => {
  if (notes.some((note) => note.text.trim() === '')) {
    return {
      isValid: false,
      message: VALIDATION_ERRORS['EMPTY_NOTE'],
    };
  }
  return { isValid: true, message: '' };
};

const validateTalkdownNotes = (talkdownNotes: string[]) => {
  if (talkdownNotes.some((note) => note.trim() === '')) {
    return {
      isValid: false,
      message: VALIDATION_ERRORS['EMPTY_TALKDOWN_NOTE'],
    };
  }
  return { isValid: true, message: '' };
};

export const validateFormData = (data?: SOPWorkflow) => {
  if (!data) {
    return { isValid: false, message: undefined };
  }
  let message = undefined;

  if (!data.siteAddress) {
    message = VALIDATION_ERRORS['REQUIRED_SITE_ADDRESS'];
  } else if (!data.siteGoogleMapLocation) {
    message = VALIDATION_ERRORS['REQUIRED_GOOGLE_MAP'];
  } else if (!validatePoints('Exception', data.exceptions).isValid) {
    message = validatePoints('Exception', data.exceptions).message;
  } else if (!validatePoints('Escalation', data.escalationPoints).isValid) {
    message = validatePoints('Escalation', data.escalationPoints).message;
  } else if (
    data.notes &&
    data.notes.length > 0 &&
    !validateNotes(data.notes).isValid
  ) {
    message = validateNotes(data.notes).message;
  } else if (
    data.talkdownNotes &&
    data.talkdownNotes.length > 0 &&
    !validateTalkdownNotes(data.talkdownNotes).isValid
  ) {
    message = validateTalkdownNotes(data.talkdownNotes).message;
  } else if (
    data.isTalkdownEnabled &&
    data.talkdowns &&
    data.talkdowns.length > 0
  ) {
    for (let index = 0; index < data.talkdowns.length; index++) {
      const talkdown = data.talkdowns[index];
      if (!talkdown.text) {
        message = `Talkdown text is required for Talkdown ${index + 1}`;
        break;
      }
    }
  }

  return { isValid: !message, message };
};
