/* eslint-disable max-lines */
import { useUpdateSOP, useCreateSOP } from '@hakimo-ui/hakimo/data-access';
import { SOPWorkflow, SOPWorkflowFormItemsType } from '@hakimo-ui/hakimo/types';
import { toast } from '@hakimo-ui/hakimo/util';
import { Alert, Button, Selectable, Tooltip } from '@hakimo-ui/shared/ui-base';
import {
  InformationCircleIcon,
  PencilSquareIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import clsx from 'clsx';
import { useEffect, useState } from 'react';
import {
  createUpdateSOPPayload,
  createCreateSOPPayload,
  getFormData,
  getInitialData,
  getWorkflowFormItems,
  updateNullValues,
  getTitleWithTimezone,
} from './utils';
import { validateFormData } from './validation';
import SOPAlarmTypeSelector from './sop-alarm-type-selector/SOPAlarmTypeSelector';
import { AddSOPModal } from './add-sop-modal/AddSOPModal';

interface Props {
  sopId: string;
  sopWorkflow?: SOPWorkflow;
  showTitle?: boolean;
  locationId?: string;
  tenantId?: string;
  alarmTypeId?: string;
  onAlarmTypeChange?: (alarmType: Selectable | null) => void;
  sopAlarmTypes?: Selectable[];
  timezone?: string;
}

export function SOPManager(props: Props) {
  const {
    sopWorkflow,
    sopId,
    showTitle = true,
    locationId,
    tenantId,
    alarmTypeId,
    onAlarmTypeChange,
    sopAlarmTypes,
    timezone,
  } = props;
  const [isEditing, setIsEditing] = useState(false);
  const [isAddSOPModalOpen, setIsAddSOPModalOpen] = useState(false);
  const [updatedSOPWorkflow, setUpdatedSOPWorkflow] = useState<
    SOPWorkflow | undefined
  >(() => (sopWorkflow ? updateNullValues(sopWorkflow, timezone) : undefined));
  const [validationState, setValidationState] = useState({
    isValid: true,
    message: '',
  });
  const onUpdateSuccess = () => {
    setIsEditing(false);
    toast('SOP workflow updated successfully', { type: 'success' });
  };

  const onCreateSuccess = () => {
    setIsEditing(false);
    toast('SOP workflow created successfully', { type: 'success' });
  };

  const updateMutation = useUpdateSOP(sopId, onUpdateSuccess);
  const createMutation = useCreateSOP(onCreateSuccess);

  useEffect(() => {
    if (!isEditing) {
      sopWorkflow &&
        setUpdatedSOPWorkflow(updateNullValues(sopWorkflow, timezone));
    }
  }, [isEditing, sopWorkflow, timezone]);

  const toggleEditing = () => {
    setIsEditing((prev) => {
      if (prev && !sopWorkflow) {
        setUpdatedSOPWorkflow(undefined);
      }
      return !prev;
    });
  };
  const onCancelEdit = () => {
    setIsEditing(false);
    if (!sopWorkflow) {
      setUpdatedSOPWorkflow(undefined);
    }
  };
  const openAddSOPModal = () => setIsAddSOPModalOpen(true);
  const closeAddSOPModal = () => setIsAddSOPModalOpen(false);

  const handleAlarmTypeChange = (alarmType: Selectable | null) => {
    if (onAlarmTypeChange) {
      onAlarmTypeChange(alarmType);
    }
  };

  const handleSOPCreated = (alarmType: Selectable) => {
    handleAlarmTypeChange(alarmType);
  };

  const onSubmit = () => {
    const { isValid, message } = validateFormData(updatedSOPWorkflow);

    if (isValid) {
      setValidationState({ isValid, message: '' });
    } else {
      setValidationState({ isValid, message: message || '' });
      return;
    }

    if (!sopWorkflow) {
      // Create new SOP
      if (!locationId || !tenantId) {
        toast('Missing location or tenant information', { type: 'error' });
        return;
      }
      const createPayload = createCreateSOPPayload(
        updatedSOPWorkflow!,
        tenantId,
        locationId,
        alarmTypeId
      );
      createMutation.mutate(createPayload);
    } else {
      // Update existing SOP
      const updatePayload = createUpdateSOPPayload(
        updatedSOPWorkflow!,
        alarmTypeId,
        !alarmTypeId
      );
      updateMutation.mutate(updatePayload);
    }
  };

  const enableWorkflow = () => {
    const initData = getInitialData();
    setUpdatedSOPWorkflow(initData);
    setIsEditing(true);
  };

  const renderSOPHeader = () => {
    if (!showTitle) return null;

    return (
      <div className="border-onlight-line-2 dark:border-ondark-line-2 mb-2 flex items-center justify-between border-b p-2">
        <div className="flex items-center gap-4">
          <span className="font-bold">
            Standard Operating procedure (SOP) Workflow
          </span>
          {!isEditing && (
            <SOPAlarmTypeSelector
              onChange={handleAlarmTypeChange}
              initialAlarmTypeId={alarmTypeId}
              sopAlarmTypes={sopAlarmTypes}
            />
          )}
          {!isEditing && (
            <Button onClick={toggleEditing} variant="icon">
              <span className="flex gap-2">
                <PencilSquareIcon className="h-5 w-5" />
                Edit
              </span>
            </Button>
          )}
        </div>
        <div className="flex items-center gap-2">
          {isEditing && (
            <Button onClick={toggleEditing} variant="icon" title="Cancel Edit">
              <span className="flex gap-1">
                <XMarkIcon className="h-5 w-5" />
                Cancel Edit
              </span>
            </Button>
          )}
          {!isEditing && locationId && tenantId && sopWorkflow && (
            <Button onClick={openAddSOPModal} variant="outline">
              Add/Modify SOP
            </Button>
          )}
        </div>
      </div>
    );
  };

  if (!sopWorkflow && !updatedSOPWorkflow) {
    return (
      <div className="w-full p-2">
        <div className="mt-16 flex flex-col items-center justify-center gap-4">
          <div>SOP workflow is not enabled for this location.</div>
          {showTitle && (
            <Button variant="primary" onClick={enableWorkflow}>
              Enable Workflow
            </Button>
          )}
        </div>
      </div>
    );
  }

  const currentSOPWorkflow =
    updatedSOPWorkflow ||
    (sopWorkflow ? updateNullValues(sopWorkflow, timezone) : getInitialData());
  const formData = getFormData(isEditing, currentSOPWorkflow, sopWorkflow);

  return (
    <div className="w-full p-2">
      {renderSOPHeader()}
      {isAddSOPModalOpen && locationId && tenantId && (
        <AddSOPModal
          isOpen={isAddSOPModalOpen}
          onClose={closeAddSOPModal}
          locationId={locationId}
          tenantId={tenantId}
          sopAlarmTypes={sopAlarmTypes}
          onSOPCreated={handleSOPCreated}
        />
      )}
      {(updateMutation.isError || createMutation.isError) && (
        <Alert type="error">
          {updateMutation.error?.message || createMutation.error?.message}
        </Alert>
      )}
      {!validationState.isValid && (
        <Alert type="warning">{validationState.message}</Alert>
      )}
      <div className="flex flex-col gap-4 p-4">
        {formData.map((formItem: SOPWorkflowFormItemsType) => (
          <div
            key={`${formItem.id}-${formItem.inputType}`}
            className="grid grid-cols-2 gap-4"
          >
            <div
              className={clsx(
                'flex items-center gap-2',
                formItem.isFullColSpan && 'col-span-2'
              )}
            >
              <span className="font-bold">
                {getTitleWithTimezone(
                  formItem.title,
                  formItem.showTimezone ? timezone : undefined
                )}
                :
              </span>
              {formItem.info && (
                <Tooltip text={formItem.info} size="large" colorModifier="info">
                  <InformationCircleIcon className="h-5 w-5" />
                </Tooltip>
              )}
            </div>
            {getWorkflowFormItems(
              isEditing,
              formItem.id,
              formItem.inputType,
              formItem.dropdownOptions,
              currentSOPWorkflow,
              setUpdatedSOPWorkflow,
              sopWorkflow
            )}
          </div>
        ))}
        {isEditing && (
          <div className="border-onlight-line-2 dark:border-ondark-line-2 flex gap-4 border-t px-2 py-4">
            <Button
              variant="primary"
              onClick={onSubmit}
              loading={updateMutation.isLoading || createMutation.isLoading}
            >
              Submit
            </Button>
            <Button onClick={onCancelEdit}>Cancel</Button>
          </div>
        )}
      </div>
    </div>
  );
}

export default SOPManager;
