/* eslint-disable max-lines */
import {
  Situation,
  SOPTalkdown,
  SOPTalkdownType,
  SOPWorkflow,
  UpdateSOPPayload,
  SOPNote,
  BusinessHours,
  SiteCategoryOption,
} from '@hakimo-ui/hakimo/types';
import {
  Button,
  Checkbox,
  InputField,
  Label,
  Selectable,
  Tooltip,
} from '@hakimo-ui/shared/ui-base';
import { SelectMenu } from '@hakimo-ui/hakimo/ui-elements';
import {
  InformationCircleIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';
import WorkflowFormSituation from './situation/WorkflowFormSituation';
import { WorkflowFormListItem } from './WorkflowFormListItem';
import WorkflowFormTalkdowns from './WorkflowFormTalkdowns';
import WorkflowFormNotes from './WorkflowFormNotes';
import {
  DEFAULT_SOP_SITUATIONS,
  DEFAULT_SOP_QUICK_RESOLVE,
  SOPWorkflowFormItems,
  TALKDOWN_TYPE_INFO,
} from './constants';

export const getTitleWithTimezone = (
  title: string,
  timezone?: string
): string => {
  if (!timezone) return title;
  return `${title} (${timezone})`;
};

const migrateNotesToSOPNotes = (notes: any): SOPNote[] => {
  if (!notes || !Array.isArray(notes)) return [];
  return notes.map((note: any) => {
    if (typeof note === 'string') {
      return { text: note, isRelevantForAiOperator: true };
    }
    if (typeof note === 'object' && note.text !== undefined) {
      return {
        text: note.text,
        isRelevantForAiOperator: note.isRelevantForAiOperator ?? true,
      };
    }
    return { text: '', isRelevantForAiOperator: true };
  });
};

export const updateNullValues = (
  sopWorkflow: SOPWorkflow,
  timezone?: string
) => {
  const updatedSopWorkflow = {
    ...sopWorkflow,
    notes: migrateNotesToSOPNotes(sopWorkflow?.notes),
    escalationProtocol: sopWorkflow?.escalationProtocol ?? [],
    quickResolveActions: sopWorkflow?.quickResolveActions ?? [],
    talkdownNotes: sopWorkflow?.talkdownNotes ?? [],
    businessHours: sopWorkflow?.businessHours ?? { hours: '', timezone: '' },
    siteCategory: sopWorkflow?.siteCategory ?? '',
  };
  if (timezone && updatedSopWorkflow.businessHours) {
    updatedSopWorkflow.businessHours = {
      ...updatedSopWorkflow.businessHours,
      timezone: timezone,
    };
  }
  if (!sopWorkflow.talkdowns) {
    updatedSopWorkflow.talkdowns = [];
    sopWorkflow.firstTalkdown &&
      updatedSopWorkflow.talkdowns.push(sopWorkflow.firstTalkdown);
    sopWorkflow.secondTalkdown &&
      updatedSopWorkflow.talkdowns.push(sopWorkflow.secondTalkdown);
  }
  if (sopWorkflow.isZeroTolerance === undefined) {
    updatedSopWorkflow.isZeroTolerance = false;
  }
  if (!sopWorkflow.situations || sopWorkflow.situations.length === 0) {
    updatedSopWorkflow.situations = DEFAULT_SOP_SITUATIONS;
  }
  if (
    !sopWorkflow.quickResolveActions ||
    sopWorkflow.quickResolveActions.length === 0
  ) {
    updatedSopWorkflow.quickResolveActions = DEFAULT_SOP_QUICK_RESOLVE;
  }
  return updatedSopWorkflow;
};

export const getFormData = (
  isEditing: boolean,
  editedData: SOPWorkflow,
  initData?: SOPWorkflow
) => {
  const isTalkdownEnabled = isEditing
    ? editedData.isTalkdownEnabled
    : initData?.isTalkdownEnabled;
  const currentSiteCategory = isEditing
    ? editedData.siteCategory
    : initData?.siteCategory;
  const isSiteCategorySelected =
    !currentSiteCategory ||
    currentSiteCategory === '' ||
    currentSiteCategory.startsWith('No Category Selected');

  let filteredItems = SOPWorkflowFormItems;
  if (!isTalkdownEnabled) {
    filteredItems = filteredItems.filter(
      (item) => item.inputType !== 'talkdowns'
    );
  }
  if (isSiteCategorySelected) {
    filteredItems = filteredItems.filter(
      (item) => item.inputType !== 'siteDescription'
    );
  }

  return filteredItems;
};

const getNonEditableNodes = (
  sopData: SOPWorkflow,
  itemId: keyof SOPWorkflow,
  itemType: string,
  dropdownOptions?: SiteCategoryOption[]
) => {
  const data = updateNullValues(sopData);
  switch (itemType) {
    case 'link': {
      const val = data[itemId] as string;
      return (
        <a
          href={val}
          target="_blank"
          rel="noreferrer"
          className="text-primary-500 break-words"
        >
          {val}
        </a>
      );
    }

    case 'boolean': {
      const val = data[itemId] as boolean;
      return <Checkbox checked={val} />;
    }
    case 'list': {
      const values = data[itemId] as string[];
      return values.length > 0 ? (
        <div className="col-span-2 space-y-2">
          {values.map((val, i) => (
            <li key={i}>{val}</li>
          ))}
        </div>
      ) : (
        <div>Not applicable</div>
      );
    }
    case 'notes': {
      const values = data[itemId] as SOPNote[];
      return values.length > 0 ? (
        <div className="col-span-2 space-y-2">
          {values.map((note, i) => (
            <li key={i}>{note.text}</li>
          ))}
        </div>
      ) : (
        <div>Not applicable</div>
      );
    }
    case 'talkdowns': {
      const val = data[itemId] as SOPTalkdown[];
      return (
        <ol className="col-span-2 list-inside list-decimal space-y-4">
          {val.map((item, i) => (
            <li key={i} className="list-item gap-2">
              <div className="inline-flex items-center gap-2">
                <span>{item.text}</span>
                <span className="flex items-center gap-2">
                  <Label
                    small
                    text={`${item.type.toUpperCase()} Talkdown`}
                    type="info"
                  />
                  <Tooltip
                    text={TALKDOWN_TYPE_INFO}
                    colorModifier="info"
                    position="top-right"
                    size="large"
                  >
                    <InformationCircleIcon className="h-5 w-5" />
                  </Tooltip>
                </span>
              </div>
            </li>
          ))}
        </ol>
      );
    }
    case 'situation': {
      const items = (data[itemId] as Situation[]) ?? [];
      return items.length > 0 ? (
        <WorkflowFormSituation items={items} isEditable={false} />
      ) : (
        <div>Not applicable</div>
      );
    }
    case 'dropdown': {
      const val = data[itemId] as string;
      if (dropdownOptions && dropdownOptions.length > 0) {
        const selectedOption = dropdownOptions.find(
          (option) =>
            val === `${option.key} - ${option.description}` ||
            val === option.key
        );
        return <span>{selectedOption ? selectedOption.key : '-'}</span>;
      }
      return <span>{val || '-'}</span>;
    }
    case 'siteDescription': {
      const val = data[itemId] as string;
      let description = '';
      if (val && val.includes(' - ')) {
        const parts = val.split(' - ');
        const key = parts[0];
        if (key && key !== 'No Category Selected') {
          description = parts.slice(1).join(' - ');
        }
      }
      return <span>{description || '-'}</span>;
    }
    case 'businessHours': {
      const businessHours = data[itemId] as BusinessHours;
      return <span>{businessHours?.hours || '-'}</span>;
    }
    default: // for case type: string
      return <span>{data[itemId] as string}</span>;
  }
};

const getEditableNodes = (
  data: SOPWorkflow,
  itemId: keyof SOPWorkflow,
  itemType: string,
  dropdownOptions: SiteCategoryOption[] | undefined,
  onChangeData: (val: SOPWorkflow) => void
) => {
  const valueUpdater = (
    id: keyof SOPWorkflow,
    value:
      | string
      | string[]
      | boolean
      | SOPTalkdown[]
      | Situation[]
      | SOPNote[]
      | BusinessHours
  ) => {
    const updatedData = { ...data, [id]: value };
    onChangeData(updatedData);
  };

  switch (itemType) {
    case 'boolean': {
      const val = data[itemId] as boolean;
      return (
        <Checkbox
          checked={val}
          onChange={(checked: boolean) => valueUpdater(itemId, checked)}
        />
      );
    }

    case 'list': {
      const values = data[itemId] as string[];
      return (
        <WorkflowFormListItem
          items={values}
          onChange={(items) => valueUpdater(itemId, items)}
        />
      );
    }

    case 'notes': {
      const values = data[itemId] as SOPNote[];
      return (
        <WorkflowFormNotes
          notes={values}
          onChange={(notes) => valueUpdater(itemId, notes)}
        />
      );
    }

    case 'talkdowns': {
      const val = data[itemId] as SOPTalkdown[];

      return (
        <WorkflowFormTalkdowns
          talkdowns={val}
          onChange={(updatedVal) => valueUpdater(itemId, updatedVal)}
        />
      );
    }

    case 'situation': {
      const val = (data[itemId] as Situation[]) ?? [];
      return (
        <WorkflowFormSituation
          items={val}
          onChange={(updatedVal) => valueUpdater(itemId, updatedVal)}
        />
      );
    }

    case 'dropdown': {
      if (dropdownOptions && dropdownOptions.length > 0) {
        const currentValue = data[itemId] as string;
        const selectedOption = dropdownOptions.find((option) =>
          currentValue.startsWith(`${option.key} - `)
        );

        return (
          <SelectMenu
            value={selectedOption}
            items={dropdownOptions}
            displayValue={(item) => (item ? item.key : 'Select a category')}
            onChange={(option) => {
              const optionValue = option
                ? `${option.key} - ${option.description}`
                : '';
              valueUpdater(itemId, optionValue);
            }}
            id={(item) => item.key}
          />
        );
      }
      return null;
    }

    case 'siteDescription': {
      const currentValue = data[itemId] as string;
      let description = '';
      if (currentValue && currentValue.includes(' - ')) {
        description = currentValue.split(' - ').slice(1).join(' - ');
      }

      return (
        <InputField
          type="text"
          value={description}
          onChange={(e) => {
            let key = '';
            if (currentValue && currentValue.includes(' - ')) {
              key = currentValue.split(' - ')[0];
            }
            const newValue = key ? `${key} - ${e.target.value}` : '';
            valueUpdater(itemId, newValue);
          }}
        />
      );
    }

    case 'businessHours': {
      const businessHours = data[itemId] as BusinessHours;
      return (
        <InputField
          type="text"
          value={businessHours?.hours || ''}
          onChange={(e) => {
            valueUpdater(itemId, {
              hours: e.target.value,
              timezone: businessHours?.timezone || '',
            });
          }}
        />
      );
    }

    default: {
      // case: link and string
      const dataVal = data[itemId] as string;
      return (
        <InputField
          type="text"
          value={dataVal}
          onChange={(e) => valueUpdater(itemId, e.target.value)}
        />
      );
    }
  }
};

export const getWorkflowFormItems = (
  isEdit: boolean,
  itemId: keyof SOPWorkflow,
  itemType: string,
  dropdownOptions: SiteCategoryOption[] | undefined,
  updatedData: SOPWorkflow,
  onChangeUpdatedData: (val: SOPWorkflow) => void,
  data?: SOPWorkflow
) => {
  if (isEdit) {
    return getEditableNodes(
      updatedData,
      itemId,
      itemType,
      dropdownOptions,
      onChangeUpdatedData
    );
  } else {
    return data
      ? getNonEditableNodes(data, itemId, itemType, dropdownOptions)
      : '';
  }
};

export const getInitialData = (): SOPWorkflow => {
  return {
    exceptions: [''],
    siteAddress: '',
    businessHours: { hours: '', timezone: '' } as BusinessHours,
    siteGoogleMapLocation: '',
    siteCategory: '',
    escalationPoints: [''],
    isTalkdownEnabled: true,
    notes: [{ text: '', isRelevantForAiOperator: true }],
    situations: [...DEFAULT_SOP_SITUATIONS],
    escalationProtocol: [''],
    quickResolveActions: [...DEFAULT_SOP_QUICK_RESOLVE],
    talkdowns: [
      {
        text: '',
        type: SOPTalkdownType.STATIC,
      },
    ],
    talkdownNotes: [''],
  };
};

export const prepareSOPPayload = (
  sopWorkflow: SOPWorkflow
): { sop_workflow: SOPWorkflow } => {
  // Create a copy to avoid mutating the original
  const updatedWorkflow = { ...sopWorkflow };

  // Remove firstTalkdown and secondTalkdown fields if talkdowns are available
  if (updatedWorkflow.talkdowns && updatedWorkflow.talkdowns.length > 0) {
    delete updatedWorkflow.firstTalkdown;
    delete updatedWorkflow.secondTalkdown;
  }

  return {
    sop_workflow: updatedWorkflow,
  };
};

export const createUpdateSOPPayload = (
  sopWorkflow: SOPWorkflow,
  alarmTypeId?: string | null,
  clearAlarmType?: boolean
): UpdateSOPPayload => {
  const payload = prepareSOPPayload(sopWorkflow);

  return {
    sop_text: JSON.stringify(payload),
    alarm_type_id: alarmTypeId || undefined,
    clear_alarm_type: clearAlarmType,
  };
};

export const createCreateSOPPayload = (
  sopWorkflow: SOPWorkflow,
  tenantId: string,
  locationId: string,
  alarmTypeId?: string | null
) => {
  const payload = prepareSOPPayload(sopWorkflow);

  return {
    sop_text: JSON.stringify(payload),
    tenant_id: tenantId,
    location_id: locationId,
    alarm_type_id: alarmTypeId || undefined,
  };
};

// Utility functions for SOP components
export const displayAlarmTypeValue = (item?: Selectable) => {
  return item?.name || '';
};

export const filterAlarmTypesByQuery = (
  items: Selectable[],
  query: string
): Selectable[] => {
  if (!query) {
    return items;
  }
  return items.filter((item) =>
    item.name.toLowerCase().includes(query.toLowerCase())
  );
};

export const createAlarmTypeChangeHandler = (
  setSelectedAlarmType: (value: Selectable | undefined) => void,
  onChange?: (value: Selectable) => void
) => {
  return (value: Selectable) => {
    setSelectedAlarmType(value);
    onChange?.(value);
  };
};

export const createSOPAlarmTypeChangeHandler = (
  setSelectedAlarmType: (value: Selectable) => void,
  onChange: (alarmType: Selectable | null) => void
) => {
  return (value: Selectable | null) => {
    if (value) {
      setSelectedAlarmType(value);
      onChange(value.id === 'default' ? null : value);
    }
  };
};

// Business logic functions for SOP operations
export const createFilteredAlarmTypesGetter = (
  getAlarmTypesByName: (query: string) => Promise<Selectable[]>,
  sopAlarmTypes: Selectable[]
) => {
  return async (query: string): Promise<Selectable[]> => {
    try {
      const alarmTypes = await getAlarmTypesByName(query);
      if (sopAlarmTypes && sopAlarmTypes.length > 0) {
        const existingIds = sopAlarmTypes.map((type: Selectable) => type.id);
        return alarmTypes.filter(
          (type: Selectable) => !existingIds.includes(type.id)
        );
      }
      return alarmTypes;
    } catch (error) {
      return [];
    }
  };
};

export const createSOPPayloadFromDefaults = async (
  defaultSOPQuery: { refetch: () => Promise<any> },
  tenantId: string,
  locationId: string,
  alarmTypeId: string
) => {
  const result = await defaultSOPQuery.refetch();
  let sopWorkflow;

  if (result.isSuccess && result.data?.sop_workflow) {
    sopWorkflow = { ...result.data.sop_workflow };
  } else {
    sopWorkflow = updateNullValues(getInitialData());
  }

  return {
    sop_text: JSON.stringify({ sop_workflow: sopWorkflow }),
    tenant_id: tenantId,
    location_id: locationId,
    alarm_type_id: alarmTypeId,
  };
};

interface DeleteConfirmationProps {
  alarmType: Selectable;
  isLoading: boolean;
  onConfirm: () => void;
  onCancel: () => void;
}
export const DeleteConfirmation = ({
  alarmType,
  isLoading,
  onConfirm,
  onCancel,
}: DeleteConfirmationProps) => (
  <div className="text-status-red dark:border-status-red/20 dark:bg-status-red/10 rounded-md border border-red-200 bg-red-50 px-4 py-3 text-sm dark:text-red-200">
    <div className="flex items-start gap-3">
      <div className="flex-shrink-0">
        <ExclamationTriangleIcon className="h-5 w-5" />
      </div>
      <div className="flex-1">
        <div className="flex gap-2">
          <span className="font-bold">Confirm Deletion:</span>
          <span>Delete SOP for "{alarmType.name}"?</span>
        </div>
        <div className="mt-1 text-xs">This action cannot be undone.</div>
        <div className="mt-3 flex gap-2">
          <Button
            onClick={onConfirm}
            variant="error"
            disabled={isLoading}
            loading={isLoading}
          >
            {isLoading ? 'Deleting...' : 'Delete'}
          </Button>
          <Button onClick={onCancel} variant="outline" disabled={isLoading}>
            Cancel
          </Button>
        </div>
      </div>
    </div>
  </div>
);

export const createInlineDeleteHandlers = (
  setDeleteConfirmation: (alarmType: Selectable | null) => void,
  deleteMutation: { mutate: (data: null) => void; isLoading: boolean },
  sopQuery: { data?: { id?: string } }
) => ({
  handleDeleteRequest: (alarmType: Selectable) => {
    setDeleteConfirmation(alarmType);
  },
  handleConfirmDelete: () => {
    if (sopQuery.data?.id) {
      deleteMutation.mutate(null);
    }
  },
  handleCancelDelete: () => {
    setDeleteConfirmation(null);
  },
});

interface TalkdownNotesProps {
  notes: string[];
}
export const TalkdownNotes = ({ notes }: TalkdownNotesProps) => {
  if (!notes || notes.length === 0) return null;
  return (
    <div className="py-2">
      <div className="text-primary-800 dark:text-primary-400 mb-1 font-sans font-semibold">
        Talkdown Notes
      </div>
      <ul className="list-disc space-y-2">
        {notes.map((note, i) => (
          <li key={i} className="ml-4 list-item">
            {note}
          </li>
        ))}
      </ul>
    </div>
  );
};
