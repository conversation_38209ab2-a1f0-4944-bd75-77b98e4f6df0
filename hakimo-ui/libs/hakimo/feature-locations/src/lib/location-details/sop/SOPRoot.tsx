import { useSOP, useSOPAlarmTypes } from '@hakimo-ui/hakimo/data-access';
import { SOPManager } from '@hakimo-ui/hakimo/feature-shared';
import { QueryResult } from '@hakimo-ui/hakimo/ui-elements';
import { Selectable } from '@hakimo-ui/shared/ui-base';
import { useState, useCallback } from 'react';

interface Props {
  locationId: string;
  tenantId: string;
  timezone?: string;
}

export function SOPRoot(props: Props) {
  const { locationId, tenantId, timezone } = props;
  const [currentAlarmTypeId, setCurrentAlarmTypeId] = useState<
    string | undefined
  >();
  const { data: sopAlarmTypes = [] } = useSOPAlarmTypes(locationId);

  const sopQuery = useSOP({
    locationId,
    alarmTypeId: currentAlarmTypeId,
  });

  const handleAlarmTypeChange = useCallback((alarmType: Selectable | null) => {
    const newAlarmTypeId = alarmType ? alarmType.id : undefined;
    setCurrentAlarmTypeId(newAlarmTypeId);
  }, []);

  const renderSOPManager = (sopWorkflow?: any, id?: string) => (
    <SOPManager
      sopWorkflow={sopWorkflow}
      sopId={id ?? ''}
      locationId={locationId}
      tenantId={tenantId}
      alarmTypeId={currentAlarmTypeId}
      onAlarmTypeChange={handleAlarmTypeChange}
      sopAlarmTypes={sopAlarmTypes}
      timezone={timezone}
    />
  );

  return (
    <QueryResult
      queryResult={sopQuery}
      loadingText="Loading SOP..."
      errorFormatter={() => renderSOPManager()}
    >
      {({ sop_workflow, id }) => renderSOPManager(sop_workflow, id)}
    </QueryResult>
  );
}

export default SOPRoot;
