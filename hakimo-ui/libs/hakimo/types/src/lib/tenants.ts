export interface TenantsDTO {
  tenants: Tenant[];
  total: number;
}

export interface Tenant {
  id: string;
  name: string;
  alarmProcessingConfig?: {
    alarm_types: string[];
  };
  config?: {
    visonHosts: string[];
    escalationType?: string;
    rspndrApiKey?: string;
    rspndrApiSecret?: string;
    rspndrTenantId?: string;
    rspndrUrl?: string;
    customerNumber?: string;
    postalCode?: string;
    rspndrCancelUrl?: string;
    rspndrNotifyUrl?: string;
  };
}
