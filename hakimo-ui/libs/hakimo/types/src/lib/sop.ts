export enum SOPTalkdownType {
  STATIC = 'static',
  DYANMIC = 'dynamic',
}

export type SOPTalkdown = {
  type: SOPTalkdownType;
  text: string;
};

export enum SituationColor {
  RED = 'red',
  GREEN = 'green',
  BLUE = 'blue',
}
export interface Situation {
  label: string;
  color: SituationColor;
}

export interface SOPNote {
  text: string;
  isRelevantForAiOperator: boolean;
}

export interface BusinessHours {
  hours: string;
  timezone: string;
}

export type SOPWorkflow = {
  firstTalkdown?: SOPTalkdown;
  secondTalkdown?: SOPTalkdown;
  exceptions: string[];
  siteAddress: string;
  siteGoogleMapLocation: string;
  siteCategory?: string;
  businessHours: BusinessHours;
  isTalkdownEnabled: boolean;
  escalationPoints: string[];
  notes: SOPNote[];
  situations: Situation[];
  escalationProtocol: string[];
  quickResolveActions?: string[];
  isZeroTolerance?: boolean;
  talkdowns?: SOPTalkdown[];
  talkdownNotes?: string[];
};

export type ScanSOPContact = {
  name: string;
  phoneNumber: string;
  role?: string;
};

export type ScanTenantSOP = {
  address: string;
  emergencySituations: string[];
  nonEmergencySituations: string[];
  exceptions: string[];
  emergencyContact: ScanSOPContact[];
  nonEmergencyContact: ScanSOPContact[];
  isTalkdownEnabled?: boolean;
};

export interface SOPDTO {
  id?: string;
  sop_workflow?: SOPWorkflow;
  scan_sop?: ScanTenantSOP;
  alarm_type_id?: string;
}

export interface UpdateSOPPayload {
  sop_text: string;
  alarm_type_id?: string;
  clear_alarm_type?: boolean;
}

export interface CreateSOPPayload {
  sop_text: string;
  tenant_id: string;
  location_id: string;
  alarm_type_id?: string;
  clear_alarm_type?: boolean;
}

export interface SOPWorkflowState {
  logCallFromSOP: (twilioCallSid: string, toName: string) => void;
}

export interface SOPWorkflowFormItemsType {
  title: string;
  id: keyof SOPWorkflow;
  inputType: string;
  info?: string;
  isFullColSpan?: boolean;
  showTimezone?: boolean;
  dropdownOptions?: SiteCategoryOption[];
}

export interface SiteCategoryOption {
  key: string;
  description: string;
}
